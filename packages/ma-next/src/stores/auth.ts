import { User } from '@supabase/supabase-js';
import { createStore } from './createStore';
import { supabase } from '../lib/supabase';

export interface AuthState {
  isAuthenticated: boolean;
  isAnonymous: boolean;
  user: User | null;
  error: string | null;
  verificationPayload: {
    email: string;
    password?: string;
  } | null;
  verificationMode: 'signup' | 'upgrade' | 'reset' | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  isAnonymous: false,
  user: null,
  error: null,
  verificationPayload: null,
  verificationMode: null,
};

/**
 * `AuthStore` is a singleton class responsible for managing user authentication state
 * using Supabase. It handles user sign-in, sign-up, sign-out, password reset,
 * and OTP verification. The store uses Zustand for state management and persists
 * authentication state across sessions.
 */
class AuthStore {
  private static instance: AuthStore;
  private store;

  private constructor() {
    this.store = createStore<AuthState>(initialState);
    this.initializeAuth();
  }

  /**
   * Gets the singleton instance of `AuthStore`.
   * @returns The singleton instance of `AuthStore`.
   */
  static getInstance(): AuthStore {
    if (!AuthStore.instance) {
      AuthStore.instance = new AuthStore();
    }
    return AuthStore.instance;
  }

  /**
   * Initializes the authentication state by checking the current Supabase session
   * and setting up a listener for authentication state changes.
   * @private
   */
  private async initializeAuth() {
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (session?.user) {
      const isAnonymous = session.user.is_anonymous;
      this.store.setState({
        isAuthenticated: !isAnonymous,
        isAnonymous,
        user: session.user,
        error: null,
      });
    }

    supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        const isAnonymous = session.user.is_anonymous;
        this.store.setState({
          isAuthenticated: !isAnonymous,
          isAnonymous,
          user: session.user,
          error: null,
        });
      } else if (event === 'SIGNED_OUT') {
        this.store.setState(initialState);
      }
    });
  }

  /**
   * Handles the authentication flow for both login and registration.
   * @param email The user's email address.
   * @param password The user's password.
   * @param type The type of authentication flow, either "login" or "register".
   * @private
   */
  private async handleAuthFlow(email: string, password: string, type: 'login' | 'register') {
    try {
      this.store.setState({ error: null });

      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        if (type === 'register') {
          const { error } = await supabase.auth.signUp({
            email,
            password,
          });

          if (error) throw error;

          this.store.setState({
            verificationMode: 'signup',
            verificationPayload: { email },
            error: null,
          });
        } else {
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (error) throw error;

          if (data.user) {
            this.store.setState({
              isAuthenticated: true,
              isAnonymous: false,
              user: data.user,
              error: null,
            });
          }
        }
      } else if (currentUser.is_anonymous) {
        // Try to update the anonymous user
        const { error: updateError } = await supabase.auth.updateUser({
          email,
        });

        if (updateError) {
          // Email exists, try to sign in
          const { data, error: signInError } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (signInError) throw signInError;

          if (data.user) {
            const { error: rpcError } = await supabase.rpc('migrate_anonymous_user', {
              anon_id: currentUser.id,
            });

            if (rpcError) console.log('RPC error:', rpcError);

            this.store.setState({
              isAuthenticated: true,
              isAnonymous: false,
              user: data.user,
              error: null,
            });
            return;
          }
        } else {
          // Email update successful, store pending upgrade
          this.store.setState({
            verificationPayload: { email, password },
            verificationMode: 'upgrade',
            error: null,
          });

          return { verificationPending: true };
        }
      } else {
        console.log('User already logged in:', currentUser);
      }
    } catch (error) {
      console.error(`${type} error:`, error);
      this.store.setState({
        error: error instanceof Error ? 'auth.failedToAuth' : `auth.failedTo${type}`,
      });
      throw error;
    }
  }

  /**
   * Verifies the OTP (One-Time Password) code sent to the user's email.
   * @param code The OTP code to verify.
   * @returns A promise that resolves with an object containing either an error or null.
   */
  async verifyOtp(code: string) {
    try {
      const { verificationMode, verificationPayload } = this.store.getState();

      const { error } = await supabase.auth.verifyOtp({
        email: verificationPayload?.email || '',
        token: code,
        type:
          (verificationMode === 'reset' && 'recovery') ||
          (verificationMode === 'upgrade' && 'email_change') ||
          'email',
      });

      if (error) throw error;

      if (verificationPayload?.password) {
        this.setNewPassword(verificationPayload.password);
      }

      switch (verificationMode) {
        case 'reset':
          this.store.setState({
            verificationMode: null,
            // leave payload alone so we can login with the new email afterwards.
            error: null,
          });
          return [null, 'setPassword'];
        default:
          this.store.setState({
            verificationMode: null,
            verificationPayload: null,
            error: null,
          });
          return [null, null];
      }
    } catch (error) {
      console.error('OTC verification error:', error);
      this.store.setState({
        error: error instanceof Error ? 'auth.failedToVerifyCode' : 'auth.failedToVerifyCode',
      });
      return [error, null];
    }
  }

  /**
   * Initiates the password reset flow by sending a reset password email to the user.
   * @param email The user's email address.
   * @throws {Error} If there is an error sending the reset email.
   */
  async resetPassword(email: string) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);

      if (error) throw error;

      this.store.setState({
        verificationMode: 'reset',
        verificationPayload: { email },
        error: null,
      });
    } catch (error) {
      console.error('Password reset error:', error);
      this.store.setState({
        error:
          error instanceof Error ? 'auth.failedToSendResetEmail' : 'auth.failedToSendResetEmail',
      });
      throw error;
    }
  }

  async setNewPassword(password: string, doLogin = false) {
    try {
      await supabase.auth.updateUser({ password });

      const email = this.store.getState().verificationPayload?.email;
      if (doLogin && email) {
        await supabase.auth.signInWithPassword({
          email,
          password,
        });
      }
    } catch (error) {
      console.error('Error setting new password:', error);
      this.store.setState({
        error:
          error instanceof Error ? 'auth.failedToSetNewPassword' : 'auth.failedToSetNewPassword',
      });
      throw error;
    }
  }

  /**
   * Resets the login form state by clearing any errors, verification modes, or pending upgrades.
   */
  resetLoginForm() {
    this.store.setState({
      error: null,
      verificationMode: null,
      verificationPayload: null,
    });
  }

  /**
   * Clears the current error message.
   */
  clearError() {
    this.store.setState({ error: null });
  }

  /**
   * Logs in a user with the provided email and password.
   * @param email The user's email address.
   * @param password The user's password.
   * @returns A promise that resolves when the login process is complete.
   */
  async login(email: string, password: string) {
    return this.handleAuthFlow(email, password, 'login');
  }

  /**
   * Registers a new user with the provided email and password.
   * @param email The user's email address.
   * @param password The user's password.
   * @returns A promise that resolves when the registration process is complete.
   */
  async register(email: string, password: string) {
    return this.handleAuthFlow(email, password, 'register');
  }

  /**
   * Signs in a user using Google OAuth.
   * @returns A promise that resolves when the Google sign-in process is complete.
   */
  async signInWithGoogle() {
    try {
      this.store.setState({ error: null });

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
      });

      if (error) throw error;

      // The actual sign-in will be handled by the auth state change listener
      // when the user returns from the OAuth flow
    } catch (error) {
      console.error('Google sign-in error:', error);
      this.store.setState({
        error: 'auth.failedToSignInWithGoogle',
      });
      throw error;
    }
  }

  /**
   * Logs out the current user.
   * @returns A promise that resolves when the logout process is complete.
   */
  async logout() {
    await supabase.auth.signOut();
    this.store.setState(initialState);
  }

  /**
   * A hook to access the authentication state.
   * @returns Auth store state
   */
  useAuthState() {
    return this.store.useStoreState();
  }
}

const authStore = AuthStore.getInstance();
export { authStore };
