import React, { useState } from 'react';
import { GoogleIcon } from 'components/icons/providers';
import { M } from 'intl';
import { Spinner } from 'components/Spinner';
import clsx from 'clsx';

interface GoogleSignInButtonProps {
  onClick: () => Promise<void>;
  disabled?: boolean;
  className?: string;
  isSignup?: boolean;
}

function GoogleSignInButton({
  onClick,
  disabled = false,
  className,
  isSignup,
}: GoogleSignInButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (disabled || isLoading) return;

    setIsLoading(true);
    try {
      await onClick();
    } catch (error) {
      // Error handling is done in the auth store
      console.error('Google sign-in error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={clsx(
        'w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg',
        'bg-white hover:bg-gray-50 text-gray-700 font-medium transition-colors',
        'dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700',
        'focus:ring-2 focus:ring-indigo-500 focus:border-transparent',
        (disabled || isLoading) && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      {isLoading ? <Spinner className="w-5 h-5" /> : <GoogleIcon className="w-5 h-5" />}
      <span>
        {isSignup ? <M id="auth.signupWithGoogle" /> : <M id="auth.continueWithGoogle" />}
      </span>
    </button>
  );
}

export { GoogleSignInButton };
